<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use App\Traits\LivewireGeneralFunctions;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\View\View;
use Illuminate\Validation\ValidationException;

/**
 * WonTopic Livewire Component
 *
 * Handles topic text input, frequency calculation, and topic management
 * with cart integration and save/delete functionality.
 */
class WonTopic extends Component
{
    use LivewireGeneralFunctions;

    /**
     * @var int Pool identifier
     */
    public int $poolId;

    /**
     * @var array Widget configuration
     */
    public array $widget = [];

    /**
     * @var object|null User details from backend
     */
    public ?object $userDetails = null;

    /**
     * @var object|null Biorhythm configuration details
     */
    public ?object $biorythDetails = null;

    /**
     * @var string Topic text input
     */
    public string $topicText = '';

    /**
     * @var string Calculated frequency in Hz
     */
    public string $frequencyHz = '';

    /**
     * @var string Time in seconds
     */
    public string $frequencyTime = '';

    /**
     * @var int Random time value within configured bounds
     */
    public int $randomTime = 30;

    /**
     * @var int Calculated frequency based on topic
     */
    public int $calculatedFrequency = 0;

    /**
     * @var bool Whether farbklang mode is active
     */
    public bool $farbklang = false;

    /**
     * @var array Validation rules
     */
    protected $rules = [
        'topicText' => 'required|string|min:1|max:500'
    ];

    /**
     * Component mount lifecycle
     *
     * @param int $poolId Pool identifier
     * @param array $widget Widget configuration
     * @return void
     */
    public function mount(int $poolId = 0, array $widget = []): void
    {
        $this->poolId = $poolId;
        $this->widget = $widget;

        try {
            $this->initializeComponentData();
        } catch (\Exception $e) {
            Log::error('WonTopic mount error', [
                'error' => $e->getMessage(),
                'poolId' => $poolId,
                'widget' => $widget
            ]);

            $this->setDefaultValues();
        }
    }

    /**
     * Initialize component data from backend services
     *
     * @return void
     * @throws \Exception
     */
    protected function initializeComponentData(): void
    {
        // Cache user details for performance
        $cacheKey = 'user_details_' . Auth::id();
        $this->userDetails = Cache::remember($cacheKey, 300, function () {
            return getUserDetails();
        });

        if (!$this->userDetails) {
            throw new \Exception('User details not found');
        }

        // Get biorhythm details
        $this->biorythDetails = biorythVisibleDetails();

        if (!$this->biorythDetails) {
            throw new \Exception('Biorhythm details not found');
        }

        // Generate random time within bounds
        $minPrice = max(5, (int)($this->biorythDetails->gs_min_price ?? 5));
        $maxPrice = min(3600, (int)($this->biorythDetails->gs_max_price ?? 3600));
        $this->randomTime = rand($minPrice, $maxPrice);

        // Set initial topic text
        $this->topicText = $this->sanitizeInput($this->userDetails->thema_speichern ?? '');

        // Calculate frequency if topic exists
        if (!empty($this->topicText)) {
            $this->calculatedFrequency = $this->calculateFrequencySafely($this->topicText);
            $this->frequencyHz = $this->calculatedFrequency > 0 ? (string)$this->calculatedFrequency : '';
            $this->frequencyTime = (string)$this->randomTime;
        }

        // Check farbklang mode (you may need to adjust this based on your logic)
        $this->farbklang = false; // Set based on your application logic
    }

    /**
     * Set default values when initialization fails
     *
     * @return void
     */
    protected function setDefaultValues(): void
    {
        $this->userDetails = null;
        $this->biorythDetails = null;
        $this->randomTime = 30;
        $this->calculatedFrequency = 0;
        $this->topicText = '';
        $this->frequencyHz = '';
        $this->frequencyTime = '30';
        $this->farbklang = false;
    }

    /**
     * Handle property updates with rate limiting
     *
     * @param string $propertyName
     * @return void
     */
    public function updated(string $propertyName): void
    {
        // Clear validation errors
        $this->resetErrorBag($propertyName);

        // Handle topic text updates
        if ($propertyName === 'topicText') {
            if (empty(trim($this->topicText))) {
                // Clear frequency values when topic is cleared
                $this->clearFrequencyValues();
            } else {
                // Update frequency from topic with rate limiting
                $this->updateFrequencyFromTopic();
            }
        }
    }

    /**
     * Clear frequency values and reset to defaults
     *
     * @return void
     */
    protected function clearFrequencyValues(): void
    {
        $this->frequencyHz = '';
        $this->frequencyTime = '';
        $this->calculatedFrequency = 0;

        // Reset validation errors
        $this->resetErrorBag(['frequencyHz', 'frequencyTime']);
    }

    /**
     * Update frequency based on topic text with rate limiting
     *
     * @return void
     */
    protected function updateFrequencyFromTopic(): void
    {
        $userId = Auth::id() ?? 'guest';
        $key = 'frequency_calc_' . $userId;

        // Rate limit frequency calculations (10 per minute)
        if (RateLimiter::tooManyAttempts($key, 10)) {
            $seconds = RateLimiter::availableIn($key);
            $this->showToastr('warning', trans('action.frequency_generator'),
                trans('action.too_many_calculations', ['seconds' => $seconds]));
            return;
        }

        RateLimiter::hit($key, 60);

        try {
            $sanitizedTopic = $this->sanitizeInput($this->topicText);
            $newFrequency = $this->calculateFrequencySafely($sanitizedTopic);

            if ($newFrequency > 0) {
                $this->frequencyHz = (string)$newFrequency;

                // Update frequency time with new random value
                $this->updateRandomTime();
                $this->frequencyTime = (string)$this->randomTime;
            } else {
                // If frequency calculation fails, clear the frequency field
                $this->frequencyHz = '';
            }
        } catch (\Exception $e) {
            Log::warning('Frequency calculation error', [
                'error' => $e->getMessage(),
                'topic' => substr($this->topicText, 0, 50) . '...'
            ]);

            // Clear frequency on error
            $this->frequencyHz = '';
        }
    }

    /**
     * Update random time within biorhythm bounds
     *
     * @return void
     */
    protected function updateRandomTime(): void
    {
        if ($this->biorythDetails) {
            $minTime = (int)($this->biorythDetails->gs_min_price ?? 5);
            $maxTime = (int)($this->biorythDetails->gs_max_price ?? 3600);
            $this->randomTime = rand($minTime, $maxTime);
        } else {
            $this->randomTime = 30;
        }
    }

    /**
     * Calculate frequency safely with error handling
     *
     * @param string $topic
     * @return int
     */
    protected function calculateFrequencySafely(string $topic): int
    {
        try {
            // Cache calculation results
            $cacheKey = 'freq_calc_' . md5($topic);

            return Cache::remember($cacheKey, 3600, function () use ($topic) {
                $frequency = calculationFrequency($topic);

                // Validate frequency is within acceptable range
                if ($frequency < 250 || $frequency > 20000) {
                    return 0;
                }

                return (int)$frequency;
            });
        } catch (\Exception $e) {
            Log::error('Frequency calculation failed', [
                'error' => $e->getMessage(),
                'topic' => substr($topic, 0, 50) . '...'
            ]);
            return 0;
        }
    }

    /**
     * Sanitize input to prevent XSS
     *
     * @param string $input
     * @return string
     */
    protected function sanitizeInput(string $input): string
    {
        return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
    }
