<div>
    <style>
        /*
          Allow the frequency generator's dropdown to overflow its container.
          We target the specific grid item by its widget ID to avoid side effects on other widgets.
        */
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .grid-stack-item-content,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .widget-body,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .custom-widget-container,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .custom-widget-body {
            overflow: visible !important;
        }
    </style>
    <link href="{{ asset('css/dashboard/right-panel.css') }}" rel="stylesheet">

    <div class="card topicTab fade show own-topic-text-div" id="topicTab"
        style="background: #fff;background-clip: padding-box;">
        <div class="arrow"></div>
        <div class="card-body">
            <form action="">
                <div class="row">
                    <div class="col-6 d-flex align-items-center">
                        <small class="form-text text-muted d-none" id="frequency-display"></small>
                        <small class="form-text text-muted d-none" id="frequency-unit"
                            style="margin-left: 2px;">{{trans('action.unit_hertz')}}</small>
                    </div>
                    <div class="col-6 d-flex align-items-center justify-content-end">
                        @php
                        $biorythDetails = biorythVisibleDetails();
                        $randomTime = rand($biorythDetails->gs_min_price, $biorythDetails->gs_max_price);
                        $topicTimeDisplay = gmdate('i:s', $randomTime);
                        @endphp
                        <small class="form-text text-muted text-right d-none" id="topic-time-display"
                            style="color: #28a745;" data-initial-time="{{ $randomTime }}" data-price="{{ $randomTime }}">{{
                            $topicTimeDisplay }}</small>
                        <small class="form-text text-muted d-none" id="time-unit"
                            style="margin-left: 2px;">{{trans('action.unit_seconds')}}</small>
                    </div>
                </div>
                <div class="form-group topic">
                    <textarea name="" id="own_topic" rows="2" class="form-control"
                        oninput="debouncedUpdateTopicFrequency(this)"
                        placeholder="{{trans('action.topic_name_placeholder')}}">{{ $userDetails->thema_speichern }}</textarea>
                </div>
                <div class="text-center topic-btns">
                    @if(!$farbklang)
                    <button type="button" class="btn btn-primary icon"
                        onclick="btnAddCart(this)">{{trans('action.cart')}}</button>
                    @endif
                    <button type="button" class="btn btn-success icon"
                        onclick="topicSave(this,'Save')">{{trans('action.save')}}</button>
                    <button type="button" class="btn btn-danger icon"
                        onclick="topicSave(this,'Delete')">{{trans('action.delete')}}</button>
                </div>
            </form>
        </div>
    </div>
    <script src="{{ asset('js/dashboard/right-panel.js') }}"></script>
</div>
